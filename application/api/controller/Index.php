<?php

namespace app\api\controller;

use app\common\model\Articles;
use app\common\model\Users;
use app\admin\model\Annotations;

/**
 * 首页接口
 */
class Index extends Base
{
    protected $noNeedLogin = ['index', 'stats'];
    protected $noNeedRight = ['*'];

    /**
     * 获取统计数据
     * GET /api/index/stats
     */
    public function stats()
    {
        try {
            // 获取文章总数（状态为1的已发布文章）
            $totalArticles = Articles::where('status', 1)->count();

            // 获取专家总人数（is_expert为1的用户）
            $totalExperts = Users::where('is_expert', 1)->count();

            // 获取批注总数
            $totalAnnotations = Annotations::count();

            $data = [
                'total_articles' => $totalArticles,
                'total_experts' => $totalExperts,
                'total_annotations' => $totalAnnotations
            ];

            $this->success('获取统计数据成功', $data);

        } catch (\Exception $e) {
            $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }
}
