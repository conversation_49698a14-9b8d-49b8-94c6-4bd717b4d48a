<?php

use think\Route;


// API接口
// Route::group('api', function () {
//     // 投票
//         Route::get('privacy/get', 'api/Privacy/get');
// });

// API接口
Route::group('api', function () {
    // Route::post('vote', 'api/Index/vote');

    // 统计数据接口
    Route::get('stats', 'api/Index/stats');

    // 隐私政策API接口
        Route::group('privacy', function () {
        Route::get('get', 'api/Privacy/get');
        });
    // 投票
    // Route::post('vote', 'api/Index/vote');
    
    // 评论
    // Route::get('comments/:article_id', 'api/Index/comments');
    // Route::post('comment', 'api/Index/comment');
    
    // 批注
    // Route::get('annotations/:article_id', 'api/Index/annotations');
    // Route::post('annotation/add', 'api/Index/addAnnotation');
    // Route::post('annotation/edit', 'api/Index/editAnnotation');
    // Route::post('annotation/delete', 'api/Index/deleteAnnotation');
    
    // 用户
    // Route::post('user/login', 'api/User/login');
    // Route::post('user/register', 'api/User/register');
    // Route::post('user/logout', 'api/User/logout');
    // Route::post('user/profile', 'api/User/profile');
    
    // 微信(模拟)
    Route::group('wechat', function () {
        Route::get('login', 'api/Wechat/login');       // 获取state与二维码
        Route::get('status', 'api/Wechat/status');     // 轮询登录状态
        Route::post('confirm', 'api/Wechat/confirm');  // 模拟扫码确认
        Route::get('userinfo', 'api/Wechat/UserInfo'); // 获取当前用户信息
        Route::post('logout', 'api/Wechat/logout');    // 退出登录
    });

    // 文章
    Route::group('articles', function () {
        Route::get('list', 'api/Articles/index');     // 列表
        Route::get('detail/:id', 'api/Articles/detail');     // 详情
    });
    
    Route::get('stats', 'api/Index/stats');     // 获取统计数据
});
