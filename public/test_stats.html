<!DOCTYPE html>
<html>
<head>
    <title>测试统计API</title>
    <meta charset="utf-8">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>统计数据API测试</h1>
    
    <div id="stats">
        <p>文章总数: <span id="articles">加载中...</span></p>
        <p>专家总数: <span id="experts">加载中...</span></p>
        <p>批注总数: <span id="annotations">加载中...</span></p>
    </div>
    
    <div id="error" style="color: red; display: none;"></div>
    
    <script>
        $(document).ready(function() {
            // 测试统计API
            $.ajax({
                url: '/api/stats',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('API响应:', data);
                    if (data.code === 1 && data.data) {
                        $('#articles').text(data.data.total_articles || 0);
                        $('#experts').text(data.data.total_experts || 0);
                        $('#annotations').text(data.data.total_annotations || 0);
                    } else {
                        $('#error').text('API返回错误: ' + (data.msg || '未知错误')).show();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求失败:', xhr, status, error);
                    $('#error').text('请求失败: ' + error).show();
                    $('#articles, #experts, #annotations').text('--');
                }
            });
        });
    </script>
</body>
</html>
