<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
            color: #2c1810;
            margin: 0;
            padding: 0;
        }
        
        /* 英雄区域 */
        .hero-section {
            background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #cd853f 100%);
            color: #f4f1e8;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 25px;
        }
        
        /* 内容容器 - 与导航栏对齐 */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .hero-section h1 {
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .hero-section p {
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        /* 统计信息 */
        .stats-section {
            margin-bottom: 25px;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 25px 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .stats-content {
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 15px;
        }
        
        .stats-item {
            flex: 1;
            text-align: center;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #8b4513;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #5d4037;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        /* 文章列表 */
        .articles-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2c1810;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .article-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .article-content {
            display: flex;
            gap: 15px;
            align-items: flex-start;
        }
        
        .article-cover {
            width: 120px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            border: 1px solid #e9ecef;
        }
        
        .article-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .article-text {
            flex: 1;
            min-width: 0;
        }
        
        .article-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c1810;
            margin-bottom: 8px;
            line-height: 1.3;
        }
        
        .article-title a {
            color: #2c1810;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .article-title a:hover {
            color: #8b4513;
        }
        
        .article-summary {
            color: #5d4037;
            line-height: 1.5;
            margin-bottom: 10px;
            font-size: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #8b4513;
            font-size: 0.9rem;
            padding-top: 8px;
            border-top: 1px solid #e9ecef;
        }
        
        .annotation-count {
            background: #f8f9fa;
            color: #8b4513;
            padding: 4px 8px;
            border-radius: 15px;
            font-weight: 600;
            border: 1px solid #e9ecef;
        }
        
        .article-date {
            color: #8b4513;
            font-weight: 500;
        }
        
        /* 按钮 */
        .btn-primary {
            background: linear-gradient(135deg, #8b4513, #a0522d);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            color: #f4f1e8;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #a0522d, #cd853f);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }
        
        /* 加载更多按钮区域 */
        .load-more-section {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 40px;
        }
        
        /* 页脚间距调整 */
        .footer {
            margin-top: 40px !important;
        }
        
        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #e9ecef;
            border-top: 3px solid #8b4513;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 移动端适配 */
        @media (max-width: 1200px) {
            .articles-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 18px;
            }
            
            .article-cover {
                width: 110px;
                height: 75px;
            }
            
            .article-title {
                font-size: 1.2rem;
            }
        }
        
        @media (max-width: 992px) {
            .hero-section {
                padding: 35px 20px;
            }
            
            .hero-section h1 {
                font-size: 2.4rem;
            }
            
            .hero-section p {
                font-size: 1.1rem;
            }
            
            .stats-card {
                padding: 22px 18px;
            }
            
            .stats-number {
                font-size: 2.2rem;
            }
            
            .stats-label {
                font-size: 1rem;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 18px;
            }
            
            .articles-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }
            
            .article-card {
                padding: 18px;
            }
            
            .article-content {
                gap: 14px;
            }
            
            .article-cover {
                width: 105px;
                height: 72px;
            }
            
            .article-title {
                font-size: 1.1rem;
                margin-bottom: 7px;
            }
            
            .article-summary {
                font-size: 0.95rem;
                margin-bottom: 8px;
            }
            
            .article-meta {
                font-size: 0.85rem;
                padding-top: 7px;
            }
            
            .annotation-count {
                padding: 3px 7px;
                font-size: 0.8rem;
            }
            
            .btn-primary {
                padding: 10px 25px;
                font-size: 1rem;
            }
            
            .load-more-section {
                margin-top: 25px;
                margin-bottom: 35px;
            }
            
            .footer {
                margin-top: 35px !important;
            }
        }
        
        @media (max-width: 768px) {
            .hero-section {
                padding: 30px 15px;
                margin-bottom: 20px;
            }
            
            .hero-section h1 {
                font-size: 2rem;
            }
            
            .hero-section p {
                font-size: 1rem;
            }
            
            .content-container {
                padding: 0 15px;
            }
            
            .stats-section, .articles-section {
                margin-bottom: 20px;
            }
            
            .stats-card {
                padding: 20px 15px;
            }
            
            .stats-content {
                gap: 12px;
            }
            
            .stats-number {
                font-size: 1.8rem;
            }
            
            .stats-label {
                font-size: 0.9rem;
            }
            
            .section-title {
                font-size: 1.6rem;
                margin-bottom: 15px;
            }
            
            .articles-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .article-card {
                padding: 15px;
            }
            
            .article-content {
                gap: 12px;
            }
            
            .article-cover {
                width: 90px;
                height: 65px;
            }
            
            .article-title {
                font-size: 1rem;
                margin-bottom: 6px;
            }
            
            .article-summary {
                font-size: 0.9rem;
                margin-bottom: 7px;
            }
            
            .article-meta {
                font-size: 0.8rem;
                padding-top: 6px;
            }
            
            .annotation-count {
                padding: 3px 6px;
                font-size: 0.75rem;
            }
            
            .btn-primary {
                padding: 8px 20px;
                font-size: 0.9rem;
            }
            
            .load-more-section {
                margin-top: 20px;
                margin-bottom: 30px;
            }
            
            .footer {
                margin-top: 30px !important;
            }
        }
        
        @media (max-width: 576px) {
            .hero-section {
                padding: 25px 12px;
                margin-bottom: 15px;
            }
            
            .hero-section h1 {
                font-size: 1.6rem;
            }
            
            .hero-section p {
                font-size: 0.9rem;
            }
            
            .content-container {
                padding: 0 12px;
            }
            
            .stats-section, .articles-section {
                margin-bottom: 15px;
            }
            
            .stats-card {
                padding: 18px 12px;
            }
            
            .stats-content {
                gap: 10px;
            }
            
            .stats-number {
                font-size: 1.5rem;
            }
            
            .stats-label {
                font-size: 0.8rem;
            }
            
            .section-title {
                font-size: 1.3rem;
                margin-bottom: 12px;
            }
            
            .articles-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .article-card {
                padding: 12px;
            }
            
            .article-content {
                gap: 10px;
            }
            
            .article-cover {
                width: 80px;
                height: 55px;
            }
            
            .article-title {
                font-size: 0.9rem;
                margin-bottom: 5px;
            }
            
            .article-summary {
                font-size: 0.8rem;
                margin-bottom: 6px;
            }
            
            .article-meta {
                font-size: 0.75rem;
                padding-top: 5px;
            }
            
            .annotation-count {
                padding: 2px 5px;
                font-size: 0.7rem;
            }
            
            .btn-primary {
                padding: 7px 18px;
                font-size: 0.8rem;
            }
            
            .load-more-section {
                margin-top: 15px;
                margin-bottom: 25px;
            }
            
            .footer {
                margin-top: 25px !important;
            }
        }
        
        @media (max-width: 480px) {
            .hero-section {
                padding: 20px 10px;
            }
            
            .hero-section h1 {
                font-size: 1.4rem;
            }
            
            .hero-section p {
                font-size: 0.85rem;
            }
            
            .content-container {
                padding: 0 10px;
            }
            
            .stats-card {
                padding: 15px 10px;
            }
            
            .stats-content {
                gap: 8px;
            }
            
            .stats-number {
                font-size: 1.3rem;
            }
            
            .stats-label {
                font-size: 0.75rem;
            }
            
            .section-title {
                font-size: 1.1rem;
                margin-bottom: 10px;
            }
            
            .articles-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .article-card {
                padding: 10px;
            }
            
            .article-content {
                gap: 8px;
            }
            
            .article-cover {
                width: 70px;
                height: 50px;
            }
            
            .article-title {
                font-size: 0.85rem;
                margin-bottom: 4px;
            }
            
            .article-summary {
                font-size: 0.75rem;
                margin-bottom: 5px;
            }
            
            .article-meta {
                font-size: 0.7rem;
                padding-top: 4px;
            }
            
            .annotation-count {
                padding: 2px 4px;
                font-size: 0.65rem;
            }
            
            .btn-primary {
                padding: 6px 15px;
                font-size: 0.75rem;
            }
            
            .load-more-section {
                margin-top: 12px;
                margin-bottom: 20px;
            }
            
            .footer {
                margin-top: 20px !important;
            }
        }
    </style>
</head>

<body>
    <!-- 引入公共头部导航 -->
    {include file="common/header" /}

    <!-- 英雄区域 -->
    <section class="hero-section">
        <h1>佛经文章平台</h1>
        <p>传承千年智慧，阅读经典佛经，开启心灵之旅</p>
    </section>

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 统计信息 -->
        <section class="stats-section">
            <div class="stats-card">
                <div class="stats-content">
                    <div class="stats-item">
                        <div class="stats-number" id="total-articles">-</div>
                        <div class="stats-label">文章总数</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number" id="total-experts">-</div>
                        <div class="stats-label">专家总人数</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number" id="total-annotations">-</div>
                        <div class="stats-label">批注总数</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 文章列表 -->
        <section class="articles-section">
            <h2 class="section-title">所有文章</h2>
            
            <div class="articles-grid" id="articles-container">
            <!-- 动态文章内容将注入到这里 -->
            </div>

        <!-- 加载更多按钮 -->
        <div class="load-more-section">
            <button class="btn btn-primary btn-lg load-more-btn" id="loadMoreBtn">
                <i class="fa fa-refresh"></i> 查看更多文章
            </button>
        </div>
        
        <!-- 加载动画 -->
        <div class="loading" id="loadingAnimation">
            <div class="loading-spinner"></div>
            <p style="margin-top: 15px; color: #8b4513;">正在加载更多文章...</p>
        </div>
        </section>
    </div>

    <!-- 引入公共页脚 -->
    {include file="common/footer" /}

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script src="__CDN__/assets/js/frontend/api.js"></script>
    
    <script>
        $(document).ready(function() {
            const $articlesContainer = $('#articles-container');
            const $loadMoreBtn = $('#loadMoreBtn');
            const $loading = $('#loadingAnimation');

            let currentPage = 0; // 将在首次加载时加到1
            const pageSize = 6;
            let isLoading = false;
            let hasNext = true;

            // 加载统计数据
            async function loadStats() {
                try {
                    const data = await API.get('/api/stats');
                    if (data && data.code === 1 && data.data) {
                        $('#total-articles').text(data.data.total_articles || 0);
                        $('#total-experts').text(data.data.total_experts || 0);
                        $('#total-annotations').text(data.data.total_annotations || 0);
                    }
                } catch (e) {
                    console.error('加载统计数据失败:', e);
                    // 如果加载失败，显示默认值
                    $('#total-articles').text('--');
                    $('#total-experts').text('--');
                    $('#total-annotations').text('--');
                }
            }

            // 获取URL参数中的搜索关键词
            const urlParams = new URLSearchParams(window.location.search);
            const searchKeyword = urlParams.get('keyword') || '';

            // 如果有搜索关键词，更新页面标题和按钮文本
            if (searchKeyword) {
                $('.section-title').text(`搜索结果：${searchKeyword}`);
                document.title = `搜索：${searchKeyword} - 佛经智慧`;
                $loadMoreBtn.html('<i class="fa fa-refresh"></i> 查看更多搜索结果');
                $('#loadingAnimation p').text('正在加载更多搜索结果...');
            }

            function formatDate(dateString) {
                if (!dateString) return '';
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;
                const now = new Date();
                const diff = now - date;
                if (diff < 60000) return '刚刚';
                if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
                if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
                if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
                return date.toLocaleDateString('zh-CN');
            }

            function createCardHTML(item) {
                const url = item.url || ('/article/' + item.id);
                const cover = item.cover_image || 'https://via.placeholder.com/300x200/A67B5B/ffffff?text=佛经';
                const intro = item.introduction || '';
                const created = formatDate(item.created_at);
                return `
                    <div class="article-card">
                        <div class="article-content">
                            <div class="article-cover">
                                <img src="${cover}" alt="${item.title}封面" onerror="this.src='https://via.placeholder.com/300x200/A67B5B/ffffff?text=佛经'">
                            </div>
                            <div class="article-text">
                                <h3 class="article-title">
                                    <a href="${url}">${item.title}</a>
                                </h3>
                                <p class="article-summary">${intro}</p>
                                <div class="article-meta">
                                    <span class="annotation-count">
                                        <i class="fa fa-edit"></i> ${item.annotation_count || 0} 条批注
                                    </span>
                                    <span class="article-date">${created}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            async function loadNextPage() {
                if (isLoading || !hasNext) return;
                isLoading = true;
                $loadMoreBtn.hide();
                $loading.show();
                try {
                    const nextPage = currentPage + 1;
                    // 构建API参数，包含搜索关键词
                    const apiParams = { page: nextPage, pagesize: pageSize };
                    if (searchKeyword) {
                        apiParams.keyword = searchKeyword;
                    }
                    const data = await API.get('/api/articles/list', apiParams);
                    if (data && data.code === 1 && data.data) {
                        const items = data.data.items || [];
                        const total = Number(data.data.total || 0);
                        const page = Number(data.data.page || nextPage);
                        const size = Number(data.data.pagesize || pageSize);
                        // 追加渲染
                        let html = '';
                        items.forEach(item => { html += createCardHTML(item); });
                        $articlesContainer.append(html);
                        // 更新分页状态
                        currentPage = page;
                        hasNext = page * size < total;
                        // 控制按钮
                        if (hasNext) {
                            const moreText = searchKeyword ? '查看更多搜索结果' : '查看更多文章';
                            $loadMoreBtn.html(`<i class="fa fa-refresh"></i> ${moreText}`).prop('disabled', false).show();
                        } else {
                            const allText = searchKeyword ? '已显示全部搜索结果' : '已加载全部文章';
                            $loadMoreBtn.html(`<i class="fa fa-check"></i> ${allText}`).prop('disabled', true).show();
                        }
                    } else {
                        // 接口失败
                        $loadMoreBtn.html('<i class="fa fa-warning"></i> 加载失败，重试').prop('disabled', false).show();
                    }
                } catch (e) {
                    console.error('加载文章失败:', e);
                    $loadMoreBtn.html('<i class="fa fa-warning"></i> 网络异常，重试').prop('disabled', false).show();
                } finally {
                    $loading.hide();
                    isLoading = false;
                }
            }

            // 首次加载统计数据和文章
            loadStats();
            loadNextPage();

            // 绑定点击事件
            $loadMoreBtn.on('click', function() {
                loadNextPage();
            });
        });
    </script>
</body>
</html>
